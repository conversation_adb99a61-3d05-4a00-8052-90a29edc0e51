# 音频切分配置说明

## 配置音频总时长

如果您的音频文件时长不是60秒，请按以下步骤调整配置：

### 1. 修改时长常量

在 `DemoController.java` 中找到以下常量：

```java
/**
 * 假设的音频总时长（秒），可以根据实际音频文件调整
 * 如果不知道确切时长，可以设置一个合理的估计值
 */
private static final double ASSUMED_TOTAL_DURATION = 60.0;
```

将 `60.0` 修改为您的音频文件的实际时长（秒）。

### 2. 如何确定音频时长

您可以通过以下方式确定音频文件的时长：

1. **使用音频播放器**: 用任何音频播放器打开文件查看时长
2. **使用在线工具**: 上传到在线音频信息查看工具
3. **使用命令行工具** (如果安装了ffmpeg):
   ```bash
   ffprobe -i "语音切分.wav" -show_entries format=duration -v quiet -of csv="p=0"
   ```

### 3. 配置示例

假设您的音频文件时长为120秒（2分钟），修改如下：

```java
private static final double ASSUMED_TOTAL_DURATION = 120.0;
```

## 切分精度说明

### 当前实现的精度

- **字节流切分**: 基于文件大小比例进行切分
- **精度**: 取决于音频编码格式和比特率
- **适用场景**: 适合对精度要求不高的场景

### 提高精度的方法

如果需要更高精度的音频切分，建议：

1. **转换为标准WAV格式**: 使用音频转换工具将源文件转换为未压缩的WAV格式
2. **使用专业音频库**: 集成如FFmpeg等专业音频处理库
3. **音频帧级切分**: 基于音频采样率进行帧级精确切分

## 支持的音频格式

当前实现支持以下格式的字节流切分：

- WAV (推荐)
- M4A
- MP3
- 其他常见音频格式

## 故障排除

### 常见问题

1. **切分位置不准确**
   - 检查 `ASSUMED_TOTAL_DURATION` 是否设置正确
   - 确认音频文件没有静音段或元数据

2. **文件大小异常**
   - 检查源音频文件是否完整
   - 确认时间戳设置是否合理

3. **输出文件无法播放**
   - 字节流切分可能破坏音频文件结构
   - 建议使用标准WAV格式的源文件

### 调试信息

启用DEBUG日志级别可以查看详细的切分信息：

```yaml
logging:
  level:
    com.faw.work.ais.controller.DemoController: DEBUG
```

## 最佳实践

1. **使用标准WAV格式**: 获得最佳切分效果
2. **合理设置时长**: 确保 `ASSUMED_TOTAL_DURATION` 接近实际时长
3. **测试验证**: 切分后播放文件验证效果
4. **备份原文件**: 在进行切分操作前备份原始文件
