package com.faw.work.ais.aic.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.faw.work.ais.aic.model.domain.RagKnowledgePO;
import com.faw.work.ais.aic.model.request.RagKnowledgeBasePageRequest;
import com.faw.work.ais.aic.model.request.RagKnowledgeDocumentUnbindRequest;
import com.faw.work.ais.aic.model.response.RagKnowledgeDocumentBindResponse;
import com.faw.work.ais.aic.service.RagKnowledgeDocumentJoinsService;
import com.faw.work.ais.aic.service.RagKnowledgeService;
import com.faw.work.ais.common.Response;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * RAG知识库配置 控制器
 *
 * <AUTHOR> Assistant
 */
@Tag(name = "RAG知识库管理", description = "RAG知识库配置管理相关接口")
@RestController
@Slf4j
@RequestMapping("/rag-knowledge")
public class RagKnowledgeController {

    @Autowired
    private RagKnowledgeService knowledgeBaseService;

    @Autowired
    private RagKnowledgeDocumentJoinsService ragKnowledgeDocumentJoinsService;

    @Autowired
    private com.faw.work.ais.aic.feign.DmsKnowledgeFeignClient dmsKnowledgeFeignClient;

    @Operation(summary = "根据ID查询知识库", description = "[author:10200571]")
    @PostMapping("/getById")
    public Response<RagKnowledgePO> getById(@RequestParam("id") Long id) {
        return Response.success(knowledgeBaseService.getById(id));
    }

    @Operation(summary = "根据名称查询知识库", description = "[author:10200571]")
    @PostMapping("/getByName")
    public Response<RagKnowledgePO> getByName(@RequestParam("name") String name) {
        return Response.success(knowledgeBaseService.getByName(name));
    }

    @Operation(summary = "查询知识库列表", description = "[author:10200571]")
    @PostMapping("/list")
    public Response<List<RagKnowledgePO>> list(@RequestBody(required = false) RagKnowledgePO knowledgeBase) {
        return Response.success(knowledgeBaseService.getKnowledgeBaseList(knowledgeBase));
    }

    @Operation(summary = "分页查询知识库", description = "[author:10200571]")
    @PostMapping("/page")
    public Response<Page<RagKnowledgePO>> page(@RequestBody RagKnowledgeBasePageRequest request) {
        return Response.success(knowledgeBaseService.getKnowledgeBasePage(request));
    }

    @Operation(summary = "新增知识库", description = "[author:10200571]")
    @PostMapping("/add")
    public Response<Boolean> add(@RequestBody RagKnowledgePO knowledgeBase) {
        // 检查名称是否已存在
        RagKnowledgePO existingBase = knowledgeBaseService.getByName(knowledgeBase.getName());
        if (existingBase != null) {
            return Response.fail("知识库名称已存在");
        }

        if (knowledgeBase.getCreatedAt() == null) {
            knowledgeBase.setCreatedAt(LocalDateTime.now());
        }
        if (knowledgeBase.getUpdatedAt() == null) {
            knowledgeBase.setUpdatedAt(LocalDateTime.now());
        }
        return Response.success(knowledgeBaseService.save(knowledgeBase));
    }

    @Operation(summary = "更新知识库", description = "[author:10200571]")
    @PostMapping("/update")
    public Response<Boolean> update(@RequestBody RagKnowledgePO knowledgeBase) {
        // 检查名称是否已存在且不是当前记录
        RagKnowledgePO existingBase = knowledgeBaseService.getByName(knowledgeBase.getName());
        if (existingBase != null && !existingBase.getId().equals(knowledgeBase.getId())) {
            return Response.fail("知识库名称已存在");
        }

        knowledgeBase.setUpdatedAt(LocalDateTime.now());
        return Response.success(knowledgeBaseService.updateById(knowledgeBase));
    }

    @Operation(summary = "删除知识库", description = "[author:10200571]")
    @PostMapping("/delete")
    public Response<Boolean> delete(@RequestParam("id") Long id) {
        return Response.success(knowledgeBaseService.removeById(id));
    }

    @Operation(summary = "解绑知识库和文档关系", description = "[author:10200571]")
    @PostMapping("/unbind-documents")
    public Response<RagKnowledgeDocumentBindResponse> unbindDocuments(@Valid @RequestBody RagKnowledgeDocumentUnbindRequest request) {
        log.info("解绑知识库文档关系请求: {}", request);

        try {
            RagKnowledgeDocumentBindResponse response = ragKnowledgeDocumentJoinsService.unbindDocuments(request);

            if (response.getFailCount() > 0) {
                log.warn("部分文档解绑失败: 成功={}, 失败={}", response.getSuccessCount(), response.getFailCount());
            }

            return Response.success(response);

        } catch (Exception e) {
            log.error("解绑知识库文档关系失败", e);
            return Response.fail("解绑失败: " + e.getMessage());
        }
    }

    @Operation(summary = "查询知识库绑定的文档列表", description = "[author:10200571]")
    @PostMapping("/bound-documents")
    public Response<List<Long>> getBoundDocuments(@RequestParam("ragKnowledgeId") Long ragKnowledgeId) {
        log.info("查询知识库绑定的文档: ragKnowledgeId={}", ragKnowledgeId);

        try {
            List<Long> documentIds = ragKnowledgeDocumentJoinsService.getByBaseId(ragKnowledgeId)
                .stream()
                .map(join -> join.getDocumentId())
                .toList();

            return Response.success(documentIds);

        } catch (Exception e) {
            log.error("查询知识库绑定文档失败", e);
            return Response.fail("查询失败: " + e.getMessage());
        }
    }

}