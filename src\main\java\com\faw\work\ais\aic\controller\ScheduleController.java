package com.faw.work.ais.aic.controller;

import cn.hutool.json.JSONUtil;
import com.faw.work.ais.aic.common.aop.IdempotentLock;
import com.faw.work.ais.aic.model.response.FaqHitLogCleanResponse;
import com.faw.work.ais.aic.service.FaqHitLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 定时任务控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/schedule")
@Tag(name = "定时任务管理", description = "定时任务相关接口")
@Slf4j
@RequiredArgsConstructor
public class ScheduleController {

    private final FaqHitLogService faqHitLogService;


    @Scheduled(cron = "0 0 12 * * ?")
    @Operation(summary = "清理一个月前的FAQ命中日志", description = "[author:10200571]")
    @IdempotentLock
    public void scheduledCleanOldHitLogs() {
        log.info("定时任务开始 - 清理一个月前的FAQ命中日志");
        try {
            FaqHitLogCleanResponse response = faqHitLogService.cleanOldHitLogs();
            log.info("定时任务完成 - 清理结果: {}", JSONUtil.toJsonStr(response));
        } catch (Exception e) {
            log.error("定时任务清理FAQ命中日志失败", e);
        }
    }


}