package com.faw.work.ais.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.faw.work.ais.common.Response;
import com.faw.work.ais.controller.dto.AudioSplitRequest;
import com.faw.work.ais.controller.dto.AudioSplitResponse;
import com.faw.work.ais.controller.dto.TimeStampDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sound.sampled.*;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 演示控制器
 *
 * <AUTHOR>
 */
@Tag(name = "演示控制器", description = "演示功能相关接口")
@RestController
@Slf4j
@RequestMapping("/demo")
public class DemoController {

    private static final String SOURCE_AUDIO_FILE = "语音切分.wav";
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");

    /**
     * 切分语音片段接口
     *
     * @param request 切分请求
     * @return 切分结果
     */
    @Operation(summary = "切分语音片段", description = "[author:wangxin213]")
    @PostMapping("/split-audio")
    public Response<AudioSplitResponse> splitAudio(@RequestBody AudioSplitRequest request) {
        log.info("接收到语音切分请求: {}", JSONUtil.toJsonStr(request));

        try {
            // 1. 参数校验
            if (CollUtil.isEmpty(request.getTimeStamps())) {
                throw new IllegalArgumentException("时间戳列表不能为空");
            }

            // 2. 获取源音频文件
            ClassPathResource resource = new ClassPathResource(SOURCE_AUDIO_FILE);
            if (!resource.exists()) {
                throw new IllegalArgumentException("源音频文件不存在: " + SOURCE_AUDIO_FILE);
            }

            // 3. 创建输出目录
            String outputDirName = DateUtil.format(new Date(), "yyyyMMdd_HHmmss");
            Path outputDir = Paths.get("src/main/resources", outputDirName);
            Files.createDirectories(outputDir);
            log.info("创建输出目录: {}", outputDir.toAbsolutePath());

            // 4. 执行音频切分
            List<String> outputFiles = new ArrayList<>();
            for (int i = 0; i < request.getTimeStamps().size(); i++) {
                TimeStampDTO timeStamp = request.getTimeStamps().get(i);
                String outputFileName = String.format("segment_%d.wav", i + 1);
                Path outputFilePath = outputDir.resolve(outputFileName);
                
                // 切分音频片段
                splitAudioSegment(resource, outputFilePath, timeStamp);
                outputFiles.add(outputFileName);
                
                log.info("成功切分音频片段: {} -> {}", timeStamp, outputFileName);
            }

            // 5. 构建响应
            AudioSplitResponse response = new AudioSplitResponse();
            response.setOutputDirectory(outputDirName);
            response.setSegmentFiles(outputFiles);
            response.setTotalSegments(outputFiles.size());

            log.info("语音切分完成: {}", JSONUtil.toJsonStr(response));
            return Response.success(response);

        } catch (Exception e) {
            log.error("语音切分失败", e);
            throw new RuntimeException("语音切分失败: " + e.getMessage(), e);
        }
    }

    /**
     * 切分单个音频片段
     *
     * @param sourceResource 源音频资源
     * @param outputPath     输出文件路径
     * @param timeStamp      时间戳信息
     * @throws Exception 异常
     */
    private void splitAudioSegment(ClassPathResource sourceResource, Path outputPath, 
                                 TimeStampDTO timeStamp) throws Exception {
        
        // 解析时间戳
        LocalTime startTime = LocalTime.parse(timeStamp.getStartTime(), TIME_FORMATTER);
        LocalTime endTime = LocalTime.parse(timeStamp.getEndTime(), TIME_FORMATTER);
        
        // 读取源音频文件
        try (AudioInputStream sourceAudioStream = AudioSystem.getAudioInputStream(sourceResource.getInputStream())) {
            AudioFormat format = sourceAudioStream.getFormat();
            
            // 计算开始和结束的帧位置
            long startFrame = timeToFrame(startTime, format);
            long endFrame = timeToFrame(endTime, format);
            long frameLength = endFrame - startFrame;
            
            if (frameLength <= 0) {
                throw new IllegalArgumentException("结束时间必须大于开始时间");
            }
            
            // 跳过开始帧之前的数据
            long skipBytes = startFrame * format.getFrameSize();
            sourceAudioStream.skip(skipBytes);
            
            // 创建限制长度的音频流
            try (AudioInputStream segmentStream = new AudioInputStream(
                sourceAudioStream, 
                format, 
                frameLength
            )) {
                // 写入输出文件
                AudioSystem.write(segmentStream, AudioFileFormat.Type.WAVE, outputPath.toFile());
            }
        }
    }

    /**
     * 将时间转换为音频帧位置
     *
     * @param time   时间
     * @param format 音频格式
     * @return 帧位置
     */
    private long timeToFrame(LocalTime time, AudioFormat format) {
        // 计算总秒数
        double totalSeconds = time.getHour() * 3600 + time.getMinute() * 60 + time.getSecond() + 
                             time.getNano() / 1_000_000_000.0;
        
        // 计算帧位置
        return (long) (totalSeconds * format.getFrameRate());
    }
}
