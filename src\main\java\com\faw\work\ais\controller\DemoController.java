package com.faw.work.ais.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.faw.work.ais.common.Response;
import com.faw.work.ais.controller.dto.AudioSplitRequest;
import com.faw.work.ais.controller.dto.AudioSplitResponse;
import com.faw.work.ais.controller.dto.TimeStampDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 演示控制器
 *
 * <AUTHOR>
 */
@Tag(name = "演示控制器", description = "演示功能相关接口")
@RestController
@Slf4j
@RequestMapping("/demo")
public class DemoController {

    private static final String SOURCE_AUDIO_FILE = "语音切分.m4a";
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");

    /**
     * 假设的音频总时长（秒），可以根据实际音频文件调整
     * 如果不知道确切时长，可以设置一个合理的估计值
     */
    private static final double ASSUMED_TOTAL_DURATION = 60.0;

    /**
     * 切分语音片段接口
     *
     * @param request 切分请求
     * @return 切分结果
     */
    @Operation(summary = "切分语音片段", description = "[author:wangxin213]")
    @PostMapping("/split-audio")
    public Response<AudioSplitResponse> splitAudio(@RequestBody AudioSplitRequest request) {
        log.info("接收到语音切分请求: {}", JSONUtil.toJsonStr(request));

        try {
            // 1. 参数校验
            if (CollUtil.isEmpty(request.getTimeStamps())) {
                throw new IllegalArgumentException("时间戳列表不能为空");
            }

            // 2. 获取源音频文件
            ClassPathResource resource = new ClassPathResource(SOURCE_AUDIO_FILE);
            if (!resource.exists()) {
                throw new IllegalArgumentException("源音频文件不存在: " + SOURCE_AUDIO_FILE);
            }

            // 检查文件格式
            String fileInfo = detectAudioFileFormat(resource);
            log.info("检测到音频文件信息: {}", fileInfo);

            // 3. 创建输出目录
            String outputDirName = DateUtil.format(new Date(), "yyyyMMdd_HHmmss");
            Path outputDir = Paths.get("src/main/resources", outputDirName);
            Files.createDirectories(outputDir);
            log.info("创建输出目录: {}", outputDir.toAbsolutePath());

            // 4. 执行音频切分
            List<String> outputFiles = new ArrayList<>();
            for (int i = 0; i < request.getTimeStamps().size(); i++) {
                TimeStampDTO timeStamp = request.getTimeStamps().get(i);
                String outputFileName = String.format("segment_%d.m4a", i + 1);
                Path outputFilePath = outputDir.resolve(outputFileName);
                
                // 切分音频片段
                splitAudioSegment(resource, outputFilePath, timeStamp);
                outputFiles.add(outputFileName);
                
                log.info("成功切分音频片段: {} -> {}", timeStamp, outputFileName);
            }

            // 5. 构建响应
            AudioSplitResponse response = new AudioSplitResponse();
            response.setOutputDirectory(outputDirName);
            response.setSegmentFiles(outputFiles);
            response.setTotalSegments(outputFiles.size());

            log.info("语音切分完成: {}", JSONUtil.toJsonStr(response));
            return Response.success(response);

        } catch (Exception e) {
            log.error("语音切分失败", e);
            throw new RuntimeException("语音切分失败: " + e.getMessage(), e);
        }
    }

    /**
     * 切分单个音频片段
     *
     * @param sourceResource 源音频资源
     * @param outputPath     输出文件路径
     * @param timeStamp      时间戳信息
     * @throws Exception 异常
     */
    private void splitAudioSegment(ClassPathResource sourceResource, Path outputPath,
                                 TimeStampDTO timeStamp) throws Exception {

        // 解析时间戳
        LocalTime startTime = LocalTime.parse(timeStamp.getStartTime(), TIME_FORMATTER);
        LocalTime endTime = LocalTime.parse(timeStamp.getEndTime(), TIME_FORMATTER);

        // 计算时间差（秒）
        double startSeconds = timeToSeconds(startTime);
        double endSeconds = timeToSeconds(endTime);
        double duration = endSeconds - startSeconds;

        if (duration <= 0) {
            throw new IllegalArgumentException("结束时间必须大于开始时间");
        }

        // 由于原文件可能不是标准WAV格式，我们采用字节流方式进行简单切分
        // 这是一个简化的实现，假设音频数据是线性分布的
        try (InputStream inputStream = sourceResource.getInputStream()) {
            byte[] audioData = inputStream.readAllBytes();

            // 计算切分位置（基于文件大小的比例）
            long totalFileSize = audioData.length;
            log.info("源音频文件大小: {} bytes", totalFileSize);

            // 使用预设的音频总时长
            double assumedTotalDuration = ASSUMED_TOTAL_DURATION;

            // 验证时间范围
            if (endSeconds > assumedTotalDuration) {
                log.warn("结束时间 {}s 超过假设的音频总时长 {}s，将调整为最大时长", endSeconds, assumedTotalDuration);
                endSeconds = assumedTotalDuration;
            }

            long startByte = (long) (totalFileSize * (startSeconds / assumedTotalDuration));
            long endByte = (long) (totalFileSize * (endSeconds / assumedTotalDuration));

            // 确保不超出文件边界
            startByte = Math.max(0, Math.min(startByte, totalFileSize - 1));
            endByte = Math.max(startByte + 1, Math.min(endByte, totalFileSize));

            // 提取音频片段
            int segmentSize = (int) (endByte - startByte);
            if (segmentSize <= 0) {
                throw new IllegalArgumentException("计算的音频片段大小无效: " + segmentSize);
            }

            byte[] segmentData = new byte[segmentSize];
            System.arraycopy(audioData, (int) startByte, segmentData, 0, segmentData.length);

            // 写入输出文件
            Files.write(outputPath, segmentData);

            log.info("切分音频片段成功: 时间段={}s-{}s, 字节位置={}-{}, 片段大小={} bytes",
                    startSeconds, endSeconds, startByte, endByte, segmentData.length);
        }
    }

    /**
     * 将时间转换为秒数
     *
     * @param time 时间
     * @return 秒数
     */
    private double timeToSeconds(LocalTime time) {
        return time.getHour() * 3600 + time.getMinute() * 60 + time.getSecond() +
               time.getNano() / 1_000_000_000.0;
    }

    /**
     * 检测音频文件格式
     *
     * @param resource 音频资源
     * @return 文件格式信息
     */
    private String detectAudioFileFormat(ClassPathResource resource) {
        try (InputStream inputStream = resource.getInputStream()) {
            byte[] header = new byte[12];
            int bytesRead = inputStream.read(header);

            if (bytesRead >= 4) {
                // 检查文件头标识
                String headerStr = new String(header, 0, Math.min(bytesRead, 8));

                if (headerStr.startsWith("RIFF")) {
                    return "标准WAV格式文件";
                } else if (headerStr.contains("ftyp")) {
                    return "M4A/MP4格式文件 (将使用字节流方式处理)";
                } else if (headerStr.startsWith("ID3") || headerStr.contains("MP3")) {
                    return "MP3格式文件 (将使用字节流方式处理)";
                } else {
                    return "未知格式文件 (将使用字节流方式处理)，文件头: " + headerStr;
                }
            }

            return "无法读取文件头信息";
        } catch (Exception e) {
            return "检测文件格式时出错: " + e.getMessage();
        }
    }
}
