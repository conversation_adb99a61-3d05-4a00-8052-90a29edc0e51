package com.faw.work.ais.aic.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Schema(description = "话题总结DTO")
public class TopicSummaryDTO {
    /**
     * 输入
     */
    @Schema(description = "输入")
    private String input;
    /**
     * 本次接待总体情绪
     */
    @Schema(description = "本次接待总体情绪")
    private String totalEmotion;


    /**
     * 本次接待总结
     */
    @Schema(description = "本次接待总结")
    private String summary;

    /**
     * 整体表现
     */
    @Schema(description = "整体表现")
    private String overallPerformance;

    /**
     * 客户关注点
     */
    @Schema(description = "客户关注点")
    private String customerConcerns;

    /**
     * 下次沟通建议
     */
    @Schema(description = "下次沟通建议")
    private String followUpSuggestions;
}
