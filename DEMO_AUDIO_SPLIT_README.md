# 语音切分功能说明

## 功能概述
DemoController提供了语音切分功能，可以根据指定的时间戳将原始语音文件切分成多个片段。

## 接口信息
- **接口路径**: `/demo/split-audio`
- **请求方法**: POST
- **Content-Type**: application/json

## 请求参数

### AudioSplitRequest
```json
{
  "timeStamps": [
    {
      "startTime": "00:00:00",
      "endTime": "00:00:10"
    },
    {
      "startTime": "00:00:10", 
      "endTime": "00:00:20"
    }
  ]
}
```

### 参数说明
- `timeStamps`: 时间戳列表，用于指定切分的时间段
  - `startTime`: 开始时间，格式为HH:mm:ss
  - `endTime`: 结束时间，格式为HH:mm:ss

## 响应结果

### AudioSplitResponse
```json
{
  "code": "200",
  "message": "操作成功",
  "data": {
    "outputDirectory": "20250129_143025",
    "segmentFiles": [
      "segment_1.wav",
      "segment_2.wav"
    ],
    "totalSegments": 2
  }
}
```

### 响应说明
- `outputDirectory`: 输出目录名称，以时间戳命名
- `segmentFiles`: 切分后的音频文件名列表
- `totalSegments`: 总切分片段数

## 使用示例

### curl命令示例
```bash
curl -X POST "http://localhost:8080/demo/split-audio" \
  -H "Content-Type: application/json" \
  -d '{
    "timeStamps": [
      {
        "startTime": "00:00:00",
        "endTime": "00:00:10"
      },
      {
        "startTime": "00:00:10",
        "endTime": "00:00:20"
      }
    ]
  }'
```

## 注意事项

1. **源文件位置**: 源语音文件位于 `src/main/resources/语音切分.wav`
2. **输出位置**: 切分后的文件保存在 `src/main/resources/{时间戳目录}/` 下
3. **时间格式**: 时间必须使用HH:mm:ss格式，如"00:01:30"
4. **时间顺序**: 结束时间必须大于开始时间
5. **文件格式**: 输出文件格式为WAV
6. **数量限制**: 最多支持10个时间段的切分

## 技术实现

- 使用Java内置的`javax.sound.sampled`包进行音频处理
- 不依赖FFmpeg等外部工具
- 支持标准WAV格式音频文件
- 基于音频帧进行精确切分

## 错误处理

常见错误及解决方案：
- "时间戳列表不能为空": 请确保timeStamps数组不为空
- "源音频文件不存在": 请确保resources目录下存在"语音切分.wav"文件
- "结束时间必须大于开始时间": 请检查时间戳的逻辑顺序
- "时间格式错误": 请使用HH:mm:ss格式，如"00:01:30"
