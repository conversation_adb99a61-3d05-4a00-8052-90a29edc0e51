package com.faw.work.ais.controller;

import cn.hutool.json.JSONUtil;
import com.faw.work.ais.controller.dto.AudioSplitRequest;
import com.faw.work.ais.controller.dto.TimeStampDTO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;

/**
 * DemoController测试类
 *
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
public class DemoControllerTest {

    @Test
    public void testAudioSplitRequest() {
        // 创建测试数据
        TimeStampDTO timeStamp1 = new TimeStampDTO();
        timeStamp1.setStartTime("00:00:00");
        timeStamp1.setEndTime("00:00:10");

        TimeStampDTO timeStamp2 = new TimeStampDTO();
        timeStamp2.setStartTime("00:00:10");
        timeStamp2.setEndTime("00:00:20");

        AudioSplitRequest request = new AudioSplitRequest();
        request.setTimeStamps(Arrays.asList(timeStamp1, timeStamp2));

        // 打印JSON格式的请求数据
        System.out.println("测试请求数据:");
        System.out.println(JSONUtil.toJsonPrettyStr(request));
    }
}
