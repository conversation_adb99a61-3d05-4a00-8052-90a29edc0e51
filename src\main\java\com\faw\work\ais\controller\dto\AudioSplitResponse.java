package com.faw.work.ais.controller.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 音频切分响应数据传输对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "音频切分响应")
public class AudioSplitResponse {

    /**
     * 输出目录名称
     */
    @Schema(description = "输出目录名称，以时间戳命名", example = "20250129_143025")
    private String outputDirectory;

    /**
     * 切分后的音频文件列表
     */
    @Schema(description = "切分后的音频文件名列表")
    private List<String> segmentFiles;

    /**
     * 总切分片段数
     */
    @Schema(description = "总切分片段数", example = "2")
    private Integer totalSegments;
}
