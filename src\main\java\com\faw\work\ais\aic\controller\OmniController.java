package com.faw.work.ais.aic.controller;


import com.faw.work.ais.aic.service.OmniService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/llm-omni")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "音频流处理管理", description = "处理音频流相关的请求")
public class OmniController {

    @Autowired
    private  OmniService omniService;


    @Operation(summary = "处理音频流", description = "[author:10200571]")
    @PostMapping("/process-audio-stream")
    public String processAudioStream(@RequestParam @Parameter(description = "音频的URL") String audioUrl,
                                     @RequestParam(defaultValue = "这段音频在说什么") @Parameter(description = "音频对应的文本") String text) {
        return omniService.processAudioWithAudioUrl(audioUrl, text);
    }

    @Operation(summary = "通过上传音频文件处理", description = "[author:10200571]")
    @PostMapping(value = "/process-audio-file", consumes = "multipart/form-data")
    public String processAudioFile(
            @RequestParam("file") @Parameter(description = "上传的音频文件 (如 .wav, .mp3)") MultipartFile file,
            @RequestParam(defaultValue = "这段音频在说什么") @Parameter(description = "对音频的提问或指令") String prompt,
            @RequestParam(required = false) @Parameter(description = "给AI设定的角色或系统指令 (System Prompt)") String systemMessage) throws IOException {

        if (file.isEmpty()) {
            throw new IllegalArgumentException("上传的音频文件不能为空");
        }

        return omniService.processAudioWithAudioBase64(file.getBytes(), prompt, systemMessage);
    }
}
